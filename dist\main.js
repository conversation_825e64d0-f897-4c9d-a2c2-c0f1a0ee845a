"use strict";

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

var _count = require("./count");

var _count2 = _interopRequireDefault(_count);

var _useCount = (0, _count2["default"])();

var getCount = _useCount.getCount;
var clearCount = _useCount.clearCount;
var addCount = _useCount.addCount;

console.log(getCount());
console.log(addCount());
console.log(clearCount());